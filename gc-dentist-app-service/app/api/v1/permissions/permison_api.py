from typing import Annotated

from core.common.api_response import ApiResponse
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Request
from services.permission_service import PermissionService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version

router = APIRouter()


@router.get("/")
@version(1, 0)
async def get_current_user_permissions(
    request: Request,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    # TODO: get user id from token
    user_id = (
        (request.headers.get("authorization") or "").replace("Bearer ", "").strip()
    )
    user_id = int(user_id) if user_id.isdigit() else None

    if not user_id:
        return ApiResponse.error(message="User ID is required")

    permission_service = PermissionService(db_session)
    response = await permission_service.get_current_user_permissions(
        user_id, db_session
    )

    return ApiResponse.success(data=response.model_dump(mode="json"))
