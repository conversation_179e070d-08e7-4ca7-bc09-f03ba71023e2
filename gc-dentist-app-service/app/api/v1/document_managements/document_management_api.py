from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Query
from fastapi_pagination import Page
from schemas.requests.document_management_schema import (
    DocumentManagementCreateSchema,
    DocumentManagementUpdateSchema,
    DocumentQueryParams,
)
from schemas.responses.document_management_schema import (
    DocumentListSchema,
    GetDocumentSchema,
)
from services.document_managements.document_management_service import (
    DocumentManagementService,
)
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.get(
    "/{document_id:int}/patients/{patient_user_id:int}",
    response_model=GetDocumentSchema,
)
@version(1, 0)
@measure_time
async def get_document(
    patient_user_id: int,
    document_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = DocumentManagementService(db_session)
        document = await service.get_document(patient_user_id, document_id)

        return ApiResponse.success(data=document.model_dump(mode="json"))
    except CustomValueError as e:
        log.error(f"❌ Error in method get_document: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method get_document: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
            message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
        )


@router.get("/patients/{patient_user_id:int}", response_model=Page[DocumentListSchema])
@version(1, 0)
@measure_time
async def get_list_document(
    patient_user_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    filters: Annotated[DocumentQueryParams, Query()],
):
    try:
        service = DocumentManagementService(db_session)
        documents = await service.get_list(
            patient_user_id=patient_user_id, filters=filters
        )

        return ApiResponse.success(data=documents.model_dump(mode="json"))
    except Exception as e:
        log.error(f"❌ Error in method get_list_document: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.DOCUMENT_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.DOCUMENT_GET_LIST_FAILED.code,
        )


@router.post("")
@version(1, 0)
@measure_time
async def create_document_management(
    create_obj: DocumentManagementCreateSchema,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = DocumentManagementService(session=db_session)
        result = await service.create_document_management(create_obj=create_obj)
        return ApiResponse.success(
            data={"document_management_id": result},
            message=CustomMessageCode.DOCUMENT_MANAGEMENT_CREATED_SUCCESS.title,
        )
    except Exception as e:
        log.error("❌ Create Document Management Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.DOCUMENT_MANAGEMENT_CREATED_FAILED.title,
            data={
                "detail": CustomMessageCode.DOCUMENT_MANAGEMENT_CREATED_FAILED.description
            },
        )


@router.put("/{document_management_id}")
@version(1, 0)
@measure_time
async def update_document_management(
    document_management_id: int,
    update_obj: DocumentManagementUpdateSchema,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = DocumentManagementService(db_session)
        result = await service.update_document_management(
            document_management_id=document_management_id, update_obj=update_obj
        )
        return ApiResponse.success(
            data={"data": result.model_dump()},
            message=CustomMessageCode.DOCUMENT_MANAGEMENT_UPDATED_SUCCESS.title,
        )
    except Exception as e:
        log.error("❌ Update Document Management Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.DOCUMENT_MANAGEMENT_UPDATED_FAILED.title,
            data={
                "detail": CustomMessageCode.DOCUMENT_MANAGEMENT_UPDATED_FAILED.description
            },
        )


@router.delete("/{document_management_id}")
@version(1, 0)
@measure_time
async def delete_document_management(
    document_management_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = DocumentManagementService(db_session)
        await service.delete_document_management(document_management_id)
        return ApiResponse.success(
            message=CustomMessageCode.DOCUMENT_MANAGEMENT_DELETE_SUCCESS.title,
        )
    except Exception as e:
        log.error("❌ Delete Document Management Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.DOCUMENT_MANAGEMENT_DELETE_FAILED.title,
            data={
                "detail": CustomMessageCode.DOCUMENT_MANAGEMENT_DELETE_FAILED.description
            },
        )
