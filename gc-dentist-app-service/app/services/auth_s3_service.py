import uuid

from configuration.settings import configuration
from enums.s3_enum import <PERSON><PERSON><PERSON>, S3TypeObject
from schemas.requests.auth_schema import S3GeneratedPresignedUrlRequest
from services.common.validate_service import ValidateService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.s3_bucket import S3Client


class S3AuthService:
    async def generate_presigned_url(
        self,
        db_session: AsyncSession,
        obj: S3GeneratedPresignedUrlRequest,
        tenant_uuid: str,
    ):
        """
        Generate a presigned URL for S3 object access.
        """
        async with db_session:
            role_validators = {
                RoleEnum.DOCTOR.value: lambda: ValidateService().validate_doctor_users(
                    db_session=db_session,
                    doctor_ids=[obj.id],
                ),
                RoleEnum.PATIENT.value: lambda: ValidateService().validate_patient_user(
                    db_session=db_session,
                    patient_id=obj.id,
                ),
            }

            validator = role_validators.get(obj.role)
            await validator()
        return await self.s3_create_presigned_url(
            tenant_uuid=tenant_uuid,
            role=obj.role.value,
            id=obj.id,
            prefix_name=obj.prefix_name.value,
            file_names=obj.file_names,
        )

    async def s3_create_presigned_url(
        self,
        tenant_uuid: str,
        role: str,
        id: int,
        prefix_name: str,
        file_names: list[str],
    ):
        """
        Create a presigned URL for S3 object upload.
        """
        results = []
        for name in file_names:
            s3_client = await S3Client.get_instance(configuration)

            base_name, ext = name.rsplit(".", 1)
            object_name = f"{base_name}_{uuid.uuid4()}.{ext}"

            prefix = f"{tenant_uuid}/{role}/{id}/{prefix_name}"

            key = s3_client.generate_key_s3(
                prefix=prefix,
                object_name=object_name,
            )
            presigned_url = await s3_client.generate_presigned_url(
                key=key,
                type_object=S3TypeObject.PUT_OBJECT.value,
            )
            results.append(
                {
                    "file_name": name,
                    "presigned_url": presigned_url,
                    "object_name": key,
                }
            )
        return results
