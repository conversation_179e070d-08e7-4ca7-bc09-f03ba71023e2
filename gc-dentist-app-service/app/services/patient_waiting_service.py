from datetime import datetime, timezone

from core.messages import CustomMessageCode
from enums.medical_history_enum import MedicalHistoryStatus
from enums.patient_enum import PatientWaitingStatus
from schemas.requests.medical_history_schema import Medical<PERSON><PERSON><PERSON>C<PERSON>
from schemas.requests.patient_waiting_schema import (
    FilterPatientWaitingSchema,
    PatientWaitingCreate,
    PatientWaitingUpdate,
)
from schemas.responses.patient_waiting_schema import (
    ListPatientWaitingResponseSchema,
    PatientWaitingResponse,
)
from services.common.validate_service import ValidateService
from services.medical_history_service import MedicalHistoryService
from sqlalchemy import and_, asc, func, or_, select
from sqlalchemy.dialects.postgresql import aggregate_order_by, array
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ing,
    Reservation,
)


class PatientWaitingService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="validate_column_existence",
    )
    async def check_reservation_patient_waiting(
        self,
        reservation_id: int,
        db_session: AsyncSession,
    ):
        query = select(Reservation.id).where(Reservation.id == reservation_id)
        result = await db_session.execute(query)
        patient = result.scalar_one_or_none()
        if not patient:
            log.error(f"Patient not found with ID: {reservation_id}")
            raise CustomValueError(
                message=CustomMessageCode.PATIENT_WAITING_RESERVATION_NOT_FOUND.title,
                message_code=CustomMessageCode.PATIENT_WAITING_RESERVATION_NOT_FOUND.code,
            )

    async def validate_patient_waiting_data(
        self, obj: PatientWaitingCreate
    ) -> PatientWaitingCreate:
        """
        Validate the patient waiting data.

        :param obj: Patient waiting data to validate.
        :raises ValueError: If validation fails.
        """
        async with self.session:
            await ValidateService().validate_patient_user(
                patient_id=obj.patient_user_id,
                db_session=self.session,
            )
            if obj.assigned_doctors:
                await ValidateService().validate_doctor_users(
                    doctor_ids=obj.assigned_doctors,
                    db_session=self.session,
                )
            if obj.reservation_id:
                await self.check_reservation_patient_waiting(
                    obj.reservation_id, self.session
                )

        return obj

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="validate_column_existence",
    )
    async def create_patient_waiting(
        self, obj: PatientWaitingCreate
    ) -> PatientWaitingResponse:
        """
        Create a new patient waiting record.

        :param patient_waiting_data: Data for the patient waiting record.
        :return: Created patient waiting record.
        """
        async with self.session.begin():
            patient_waiting = PatientWaiting(**obj.model_dump())
            self.session.add(patient_waiting)
            await self.session.flush()
            await self.session.refresh(patient_waiting)
            return PatientWaitingResponse(patient_waiting_id=patient_waiting.id)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_patient_waiting",
    )
    async def update_patient_waiting(
        self,
        patient_waiting_id: int,
        obj: PatientWaitingUpdate,
    ) -> PatientWaitingResponse:
        """
        Update an existing patient waiting record.

        :param patient_waiting_id: ID of the patient waiting record to update.
        :param obj: Data for updating the patient waiting record.
        :return: Updated patient waiting record.
        """
        create_medical_history = False
        async with self.session.begin():
            if obj.assigned_doctors:
                await ValidateService().validate_doctor_users(
                    doctor_ids=obj.assigned_doctors,
                    db_session=self.session,
                )
            patient_waiting = await self.session.get(PatientWaiting, patient_waiting_id)
            if not patient_waiting:
                raise CustomValueError(
                    message=CustomMessageCode.PATIENT_WAITING_NOT_FOUND.title,
                    message_code=CustomMessageCode.PATIENT_WAITING_NOT_FOUND.code,
                )

            if (
                patient_waiting.status == PatientWaitingStatus.SCHEDULED.value
                and obj.status == PatientWaitingStatus.RESOLVED.value
            ):
                create_medical_history = True

            for field, value in obj.model_dump(exclude_unset=True).items():
                setattr(patient_waiting, field, value)

            await self.session.flush()
            await self.session.refresh(patient_waiting)

            if create_medical_history:
                medical_history_service = MedicalHistoryService()
                medical_history_id = (
                    await medical_history_service.create_medical_history(
                        data=MedicalHistoryCreate(
                            patient_user_id=patient_waiting.patient_user_id,
                            patient_waiting_id=patient_waiting.id,
                            doctor_user_ids=patient_waiting.assigned_doctors,
                            status=MedicalHistoryStatus.PROCESSING.value,
                            visit_start_datetime=datetime.now(timezone.utc),
                        ),
                        db_session=self.session,
                    )
                )

        return PatientWaitingResponse(
            patient_waiting_id=patient_waiting.id,
            medical_history_id=medical_history_id if create_medical_history else None,
        )

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_list_waiting_grouped",
    )
    async def get_list_waiting_grouped(self, params: FilterPatientWaitingSchema):
        default_date = datetime.now(timezone.utc).date()
        target_date = params.target_date or default_date

        patient_item_json = func.json_build_object(
            "id",
            PatientWaiting.id,
            "room_number",
            PatientWaiting.room_number,
            "visit_start_date",
            PatientWaiting.visit_start_date,
            "visit_start_time",
            PatientWaiting.visit_start_time,
            "visit_end_date",
            PatientWaiting.visit_end_date,
            "visit_end_time",
            PatientWaiting.visit_end_time,
            "status",
            PatientWaiting.status,
            "emergency_flag",
            PatientWaiting.emergency_flag,
            "assigned_doctors",
            PatientWaiting.assigned_doctors,
            "patient_profile",
            func.json_build_object(
                "last_name",
                PatientProfile.last_name,
                "first_name",
                PatientProfile.first_name,
                "last_name_kana",
                PatientProfile.last_name_kana,
                "first_name_kana",
                PatientProfile.first_name_kana,
                "gender",
                PatientProfile.gender,
                "date_of_birth",
                PatientProfile.date_of_birth,
                "is_adult",
                PatientUser.is_adult,
                "patient_no",
                PatientUser.patient_no,
                "uuid",
                PatientUser.uuid,
                "phone",
                PatientProfile.phone,
                "home_phone",
                PatientProfile.home_phone,
                "parent_name",
                PatientProfile.parent_name,
                "address_1",
                PatientProfile.address_1,
                "address_2",
                PatientProfile.address_2,
                "address_3",
                PatientProfile.address_3,
            ),
        )

        conditions = [
            PatientWaiting.visit_start_date == target_date,
            PatientWaiting.status == PatientWaitingStatus.SCHEDULED.value,
        ]

        if params.search:
            search = params.search.lower()
            name_filters = [
                func.concat(
                    PatientProfile.last_name, " ", PatientProfile.first_name
                ).ilike(f"%{search}%"),
                func.concat(
                    PatientProfile.last_name_kana, " ", PatientProfile.first_name_kana
                ).ilike(f"%{search}%"),
            ]

            doctor_ids = await self._get_doctor_ids_by_input_search(search)

            if doctor_ids:
                name_filters.append(
                    PatientWaiting.assigned_doctors.op("&&")(array(doctor_ids))
                )

            conditions.append(or_(*name_filters))

        stmt = (
            select(
                PatientWaiting.room_number.label("room_number"),
                func.array_agg(
                    aggregate_order_by(
                        patient_item_json, PatientWaiting.visit_start_time.asc()
                    )
                ).label("patient_list"),
            )
            .select_from(PatientWaiting)
            .join(PatientUser, PatientUser.id == PatientWaiting.patient_user_id)
            .join(PatientProfile, PatientUser.id == PatientProfile.patient_user_id)
            .where(*conditions)
            .group_by(PatientWaiting.room_number)
            .order_by(asc(PatientWaiting.room_number))
        )

        async with self.session:
            result = await self.session.execute(stmt)
            data = result.mappings().all()
            return [
                ListPatientWaitingResponseSchema(**row).model_dump(mode="json")
                for row in data
            ]

    async def _get_doctor_ids_by_input_search(self, search: str):
        condition = or_(
            func.concat(DoctorProfile.last_name, " ", DoctorProfile.first_name).ilike(
                f"%{search}%"
            ),
            func.concat(
                DoctorProfile.last_name_kana, " ", DoctorProfile.first_name_kana
            ).ilike(f"%{search}%"),
        )

        query = (
            select(DoctorUser.id)
            .join(DoctorProfile, DoctorUser.id == DoctorProfile.doctor_user_id)
            .where(and_(condition, DoctorUser.status.is_(True)))
        )

        async with self.session:
            result = await self.session.execute(query)
            doctor_ids = [row[0] for row in result.fetchall()]

            return doctor_ids
