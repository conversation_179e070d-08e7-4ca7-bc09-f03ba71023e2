#!/usr/bin/env python3
from http import HTTPStatus
from typing import Any

import msgspec
from configuration.middleware.services.jwt_auth_service import JwtAuthService
from configuration.settings import configuration
from fastapi import Request, Response
from starlette.authentication import (
    AuthCredentials,
    AuthenticationBackend,
    AuthenticationError,
)
from starlette.requests import HTTPConnection

from gc_dentist_shared.core.constants import X_TENANT_UUID
from gc_dentist_shared.core.dependencies.oauth import get_current_user_claims
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.messages import CustomMessageCode


class _AuthenticationError(AuthenticationError):
    """Override internal authentication error classes"""

    def __init__(
        self,
        *,
        code: int = HTTPStatus.UNAUTHORIZED.value,
        msg: str = CustomMessageCode.UNAUTHORIZED_ERROR.title,
        headers: dict[str, Any] | None = None,
        media_type: str = "application/json",
    ):
        self.code = code
        self.msg = msg
        self.headers = headers
        self.media_type = media_type


class JWTAuthMiddleware(AuthenticationBackend):
    """JWT Authentication Middleware"""

    @staticmethod
    def auth_exception_handler(
        conn: HTTPConnection, exc: _AuthenticationError
    ) -> Response:
        """Override internal authentication error handling"""
        payload = {"code": exc.code, "msg": exc.msg, "data": None}
        return Response(
            content=msgspec.json.encode(payload),
            status_code=exc.code,
            headers=exc.headers or None,
            media_type=exc.media_type,
        )

    async def authenticate(self, request: Request):
        """
        Authenticate the user based on JWT token in the request header.
        If authentication fails, raise an AuthenticationError.
        """
        try:
            if request.url.path in configuration.TOKEN_EXCLUDE_URLS:
                return

            auth = request.headers.get("Authorization") or request.cookies.get(
                "Authorization"
            )

            if not auth:
                log.error("❌ JWTAuthMiddleware missing Authorization header")
                raise _AuthenticationError()

            _, _, token = auth.partition("Bearer ")
            if not token:
                log.error("❌ JWTAuthMiddleware missing or malformed JWT token")
                raise _AuthenticationError()

            payload = get_current_user_claims(configuration=configuration, token=token)
            if not payload:
                log.error("❌ JWTAuthMiddleware invalid JWT token")
                raise _AuthenticationError()

            tenant_uuid = request.headers.get(X_TENANT_UUID) or payload.get(
                "tenant_uuid"
            )
            if not tenant_uuid:
                log.error("❌ JWTAuthMiddleware missing tenant UUID in request headers")
                raise _AuthenticationError(
                    code=HTTPStatus.BAD_REQUEST.value,
                    msg=CustomMessageCode.MISSING_TENANT_UUID.title,
                )
            user_id = int(payload.get("sub"))
            role_id = payload.get("role_id")
            doctor_user = await JwtAuthService().validate_auth(
                access_token=token,
                role_id=role_id,
                user_id=user_id,
                tenant_uuid=tenant_uuid,
            )
            if not doctor_user:
                log.error("❌ JWTAuthMiddleware invalid user credentials!")
                raise _AuthenticationError()

            # Note that this return uses a non-standard mode, so when authentication passes,
            # some standard features will be lost
            # Please see the standard return mode: https://www.starlette.io/authentication/
            return AuthCredentials(["authenticated"]), doctor_user

        except _AuthenticationError as e:
            raise e
        except Exception as e:
            log.error(f"❌ Exception during JWT authentication: {e}")
            raise _AuthenticationError()
