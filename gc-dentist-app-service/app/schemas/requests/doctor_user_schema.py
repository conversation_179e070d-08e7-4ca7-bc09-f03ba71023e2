from datetime import datetime
from typing import Optional

from enums.patient_enum import Gender
from pydantic import BaseModel, Field, ValidationInfo, field_validator, validate_email

from gc_dentist_shared.core.common.strings import format_phone_number
from gc_dentist_shared.core.common.utils import (
    CustomEmailStr,
    CustomPhoneNumber,
    ValidateDateString,
    convert_datetime_with_timezone,
    is_katakana,
)


class CreateDoctorPayloads(BaseModel):
    first_name: str = Field(..., min_length=1, max_length=255)
    last_name: str = Field(..., min_length=1, max_length=255)
    first_name_kana: str | None = Field(default=None, min_length=1)
    last_name_kana: str | None = Field(default=None, min_length=1)
    country_code: str = Field(min_length=1, max_length=3)
    phone: str
    email: str
    address_1: Optional[str] = None
    address_2: Optional[str] = None
    address_3: Optional[str] = None
    order_index: Optional[int] = Field(default=None, ge=0)
    date_of_birth: ValidateDateString
    gender: Gender
    prefecture_id: Optional[int] = Field(default=None, gt=0)
    postal_code: Optional[str] = Field(default=None, max_length=8)

    @field_validator("email")
    @classmethod
    def validate_email_doctor(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("Email must not be empty")

        try:
            _, email = validate_email(v)
            return email
        except Exception:
            raise ValueError("Invalid email format")

    @field_validator("postal_code")
    @classmethod
    def normalize_and_validate_postal_code(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None

        normalized_v = v.replace("-", "")
        if len(normalized_v) > 8:
            raise ValueError("Postal code cannot exceed 8 characters")

        return normalized_v

    @field_validator("order_index", mode="before")
    @classmethod
    def set_order_index_default(cls, value: Optional[int]) -> int:
        return value if value is not None else 0

    @field_validator("last_name_kana", "first_name_kana")
    @classmethod
    def validate_kana(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return None
        if not is_katakana(v):
            raise ValueError("Invalid kana format. Only Katakana are allowed")
        return v

    @field_validator("date_of_birth")
    @classmethod
    def validate_date_of_birth(cls, v: str) -> ValidateDateString:
        now_tz = convert_datetime_with_timezone(datetime.now().astimezone())
        if v >= now_tz.date():
            raise ValueError("Date of birth cannot be in the future")

        return v

    @field_validator("phone")
    @classmethod
    def validate_phone(
        cls, phone: Optional[str], info: ValidationInfo
    ) -> Optional[str]:
        country_code = info.data.get("country_code")

        if phone is not None:
            if not (10 <= len(phone) <= 11):
                raise ValueError("Phone number must be 10 or 11 digits")
            try:
                format_phone_number(phone=phone, country_code=country_code)
            except Exception:
                raise ValueError("Invalid phone number")

        return phone


class UpdateDoctorPayloads(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    first_name_kana: Optional[str] = None
    last_name_kana: Optional[str] = None
    country_code: Optional[str] = None
    phone: CustomPhoneNumber | None = None
    email: CustomEmailStr | None = None
    address_1: Optional[str] = None
    address_2: Optional[str] = None
    address_3: Optional[str] = None
    date_of_birth: Optional[str] = None
    order_index: Optional[int] = None
    gender: Optional[Gender] = None
    prefecture_id: Optional[int] = None
    postal_code: Optional[str] = None

    @field_validator("order_index", mode="before")
    @classmethod
    def set_order_index_default(cls, value: Optional[int]) -> int:
        return value if value is not None else 0
