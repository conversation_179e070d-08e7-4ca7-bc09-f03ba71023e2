[project]
name = "GC_Dentist_Shared"
version = "0.1.0"
description = "Shared models and utilities"
authors = [{ name="Your Name", email="<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.9"
dependencies = []

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]
include = ["gc_dentist_shared", "gc_dentist_shared.*"]

[tool.black]


[tool.isort]
profile = "black"


[tool.mypy]
exclude = '^gc-admin-app-service/app|authz-service/app|gc-dentist-app-service/app'

[tool.ruff]
line-length = 120
