from typing import Any, ClassVar, Optional

from core.constants import TOKEN_PREFIX
from pydantic import BaseModel, Field


class RefreshTokenRequest(BaseModel):
    client_id: str = Field(..., description="Client ID (OAuth client)")
    grant_type: str = Field(..., description="Grant type, should be 'refresh_token'")
    refresh_token: str = Field(..., description="Refresh token")

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "client_id": "client_id_example",
                "grant_type": "refresh_token",
                "refresh_token": "qvnmVyzz9XbU6juC7iyECnvvoswXdk75YDoe0gWR04CiLA6H",  # pragma: allowlist secret
            }
        }


class ValidRefreshTokenRequest(BaseModel):
    token: Optional[Any] = Field(None, description="Token object or None")
    internal_client: Optional[Any] = Field(
        None, description="Internal client object if available"
    )


class TenantInfoSchema(BaseModel):
    db_name: str
    tenant_uuid: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "db_name": "tenant_db",
                "tenant_uuid": "123e4567-e89b-12d3-a456-************",
            }
        }


class DoctorLoginRequest(BaseModel):
    client_id: str = Field(..., description="Client ID (OAuth client)")
    username: str
    password: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "client_id": "client_id",
                "username": "<EMAIL>",
                "password": "secure_password",  # pragma: allowlist secret
            }
        }


class DoctorLoginOTPRequest(BaseModel):
    client_id: str = Field(..., description="Client ID (OAuth client)")
    user_id: int
    otp: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "client_id": "client_id",
                "user_id": 1,
                "otp": "123456",
            }  # pragma: allowlist secret
        }


class DoctorLoginResponse(BaseModel):
    user_id: int


class LoginUsernamePasswordValid(BaseModel):
    tenant_uuid: str
    user: Optional[Any] = Field(None, description="User object if available")
    internal_client: Optional[Any] = Field(
        None, description="Internal client object if available"
    )


class LoginRequest(BaseModel):
    client_id: str = Field(..., description="Client ID (OAuth client)")
    tenant_slug: str
    username: str
    password: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "client_id": "client_id_example",
                "username": "john_doe",
                "password": "secure_password",  # pragma: allowlist secret
                "tenant_slug": "tenant_slug_example",
            }
        }


class LoginOTPRequest(BaseModel):
    client_id: str = Field(..., description="Client ID (OAuth client)")
    phone_number: str
    country_code: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "client_id": "client_id_example",
                "phone_number": "09051234567",
                "country_code": "+81",  # pragma: allowlist secret
            }
        }


class LoginOTPVerifyRequest(BaseModel):
    client_id: str = Field(..., description="Client ID (OAuth client)")
    phone_number: str
    country_code: str
    otp: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "client_id": "client_id_example",
                "phone_number": "09051234567",
                "country_code": "+81",  # pragma: allowlist secret
                "otp": "123456",  # pragma: allowlist secret
            }
        }


class LoginObjectValid(BaseModel):
    phone_number: str
    tenant_uuids: list[str] = Field(
        [], description="List of tenant UUIDs the phone number"
    )
    user: Optional[Any] = Field(None, description="User object if available")
    internal_client: Optional[Any] = Field(
        None, description="Internal client object if available"
    )

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "phone_number": "09051234567",
                "tenant_uuids": ["uuid1", "uuid2"],
                "user": "User object here",
                "internal_client": "Internal client object here",
            }
        }


class LogoutRequest(BaseModel):
    client_id: str = Field(..., description="Client ID (OAuth client)")


class OAuthTokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = TOKEN_PREFIX
    expires_in: int
    tenant_uuid: str

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "access_token": (
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
                    "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNT"  # pragma: allowlist secret
                ),
                "refresh_token": (
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
                    "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNT"  # pragma: allowlist secret
                ),
                "token_type": "bearer",
                "expires_in": 3600,
                "tenant_uuid": "123e4567-e89b-12d3-a456-************",
            }
        }


class OAuthTokenInternalResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = TOKEN_PREFIX
    expires_in: int
    tenant_uuids: list[str] = Field(
        [], description="List of tenant UUIDs associated with the user"
    )

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "access_token": (
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
                    "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNT"  # pragma: allowlist secret
                ),
                "refresh_token": (
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
                    "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNT"  # pragma: allowlist secret
                ),
                "token_type": "bearer",
                "expires_in": 3600,
                "tenant_uuids": ["123e4567-e89b-12d3-a456-************"],
            }
        }


class TemporaryTokenResponse(BaseModel):
    user_id: int
    bearer_token: str = TOKEN_PREFIX
    expires_in: int

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "access_token": (
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
                    "eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNT"  # pragma: allowlist secret
                ),
                "expires_in": 3600,
            }
        }


class OAuth2ConsentPayloads(BaseModel):
    client_id: str
    redirect_uri: str
    scope: str
    state: Optional[str]
