import os

os.environ["AUTHLIB_INSECURE_TRANSPORT"] = "1"

from pathlib import Path  # noqa: E402

from auth_server.oauth2 import config_oauth  # noqa: E402
from configuration.pydantic_config import spec  # noqa: E402
from configuration.settings import configuration  # noqa: E402
from flasgger import Swagger  # noqa: E402
from flask import Flask  # noqa: E402
from flask_cors import CORS  # noqa: E402


def load_rsa_private_key() -> str:
    base_dir = Path(__name__).resolve().parent
    current_kid = configuration.RSA_KEY_MANIFEST.get("current_kid")
    rsa_path = base_dir / configuration.RSA_KEY_MANIFEST.get("keys").get(
        current_kid
    ).get("private_path")
    with open(rsa_path, "rb") as f:
        return f.read()


def create_app():
    app = Flask(__name__)
    spec.register(app)
    Swagger(app, template_file=configuration.SWAGGER_TEMPLATE_FILE_PATH)

    CORS(
        app,
        resources={r"/*": {"origins": configuration.BACKEND_CORS_ORIGINS}},
        supports_credentials=True,
        allow_headers=configuration.BACKEND_CORS_ALLOW_HEADERS,
        methods=configuration.BACKEND_CORS_ALLOW_METHODS,
    )

    app.config["APP_SECRET_KEY"] = configuration.APP_SECRET_KEY

    configuration.JWT_RSA_PRIVATE_KEY = load_rsa_private_key()
    if not configuration.JWT_RSA_PRIVATE_KEY:
        print("Error load RSA private key")
        raise Exception("Error load RSA private key")

    # server.init_app(app)
    config_oauth(app)

    from api.routes import routes

    for bp in routes:
        app.register_blueprint(bp)

    return app


app = create_app()

if __name__ == "__main__":
    app.run(port=5000)
