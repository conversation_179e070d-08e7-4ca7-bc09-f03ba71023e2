from pathlib import Path
from typing import ClassVar, Literal

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    BASE_DIR: ClassVar[Path] = Path(__file__).resolve().parent

    model_config = SettingsConfigDict(
        env_file=str(BASE_DIR / ".env"),
        env_file_encoding="utf-8",
        extra="ignore",
    )

    PROJECT_NAME: str
    ENVIRONMENT: Literal["unittest", "develop", "testing", "staging", "production"]

    APP_SECRET_KEY: str = "LL@stpUe6TVS)@0U{;2-=b7<f]Ky9R"

    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_GLOBAL_DB_NAME: str
    POSTGRES_PORT: int
    DB_ECHO: bool = False
    DB_INIT: bool = False

    READ_ONLY_POSTGRES_SERVER: str
    READ_ONLY_POSTGRES_USER: str
    READ_ONLY_POSTGRES_PASSWORD: str
    READ_ONLY_POSTGRES_GLOBAL_DB_NAME: str
    READ_ONLY_POSTGRES_PORT: int

    COMMUNICATE_SECRET_KEY: str

    AES_SECRET_ID_ROTATION: str
    AWS_SECRET_ROTATION_KEY_MAPPING: dict = {}
    AWS_SECRET_CURRENT_VERSION: str
    AES_SECRET_KEY_MAPPING: str

    AWS_REGION_NAME: str | None = None
    AWS_ACCESS_KEY_ID: str | None = None
    AWS_SECRET_ACCESS_KEY: str | None = None

    # Dentist URL
    SERVICE_DENTIST_URL: str = "http://localhost:8000"
    LOGIN_ENDPOINT: str = "/v1_0/auth/login"

    JWT_SECRET_KEY: str = "01r&V3KZv{1fZ.MgIK]z6futXHKgod1["
    # JWT_ALGORITHM: str = "HS256"
    JWT_ALGORITHM: str = "RS256"
    JWT_RSA_PRIVATE_KEY: str = ""
    RSA_KEY_MANIFEST: dict = {
        "current_kid": "key_20250805",
        "keys": {
            "key_20250805": {
                "private_path": "configuration/.keys/rsa_private_key_20250805.pem",
                "public_path": "configuration/.keys/rsa_public_key_20250805.pem",
                "status": "active",
            }
        },
    }

    OAUTH_CODE_EXPIRE_MINUTES: int = 10
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15
    REFRESH_TOKEN_EXPIRE_DAYS: float = 30

    DEFAULT_CLIENT_NAME_FOR_WEB: str = "Noda-Web"
    DEFAULT_CLIENT_NAME_FOR_APP: str = "Noda-App"
    DEFAULT_INTERNAL_SCOPES: list = [
        "profile",
        "email",
    ]
    AUTHLIB_INSECURE_TRANSPORT: str = "1"

    # Twilio configuration
    TWILIO_ACCOUNT_SID: str
    TWILIO_AUTH_TOKEN: str
    TWILIO_SERVICE_SID: str
    TWILIO_MESSAGE_SERVICE_SID: str

    # SES configuration
    SES_REGION_NAME: str | None = "ap-northeast-1"
    SES_FROM_MAIL: str | None = None

    # Redis configuration
    REDIS_HOST: str
    REDIS_PORT: int
    REDIS_PASSWORD: str | None = None
    REDIS_DATABASE: int
    REDIS_TIMEOUT: int
    REDIS_SSL: bool = False

    # CORS
    BACKEND_CORS_ORIGINS: list[str] = []
    BACKEND_CORS_ALLOW_HEADERS: list[str] = ["*"]
    BACKEND_CORS_ALLOW_METHODS: list[str] = [
        "GET",
        "POST",
        "PUT",
        "OPTIONS",
        "PATCH",
        "DELETE",
    ]

    # Swagger configuration
    SWAGGER_TEMPLATE_FILE_PATH: str = "api/noda_swagger.yaml"


def get_settings() -> Settings:
    """Read configuration optimization writing method"""
    return Settings()


configuration = get_settings()
