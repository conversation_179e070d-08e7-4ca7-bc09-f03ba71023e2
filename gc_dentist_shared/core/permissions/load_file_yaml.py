import os

import yaml

BASE_DIR = os.path.dirname(os.path.abspath(__file__))


def load_yaml(path: str) -> dict:
    if not os.path.isabs(path):
        full_path = os.path.join(BASE_DIR, path)
    else:
        full_path = path

    if not os.path.exists(full_path):
        raise FileNotFoundError(f"YAML file not found: {full_path}")

    with open(full_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f) or {}


def load_tenant_permissions_yaml() -> dict:
    return load_yaml("tenant/permissions.yaml")


def load_tenant_roles_yaml() -> dict:
    return load_yaml("tenant/roles.yaml")


def load_tenant_role_permissions_yaml() -> dict:
    return load_yaml("tenant/role_permissions.yaml")


def load_central_plan_permissions_yaml() -> dict:
    return load_yaml("central/plan_permission.yaml")
