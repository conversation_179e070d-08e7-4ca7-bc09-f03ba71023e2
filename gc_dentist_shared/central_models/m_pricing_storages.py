from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, Numeric, String

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class MasterPricingStorage(CentralBase, DateTimeMixin):
    """Master pricing storage table in central database"""

    __tablename__ = "m_pricing_storages"

    storage_key_id = Column(Integer, primary_key=True, autoincrement=False)
    name = Column(String(length=255), nullable=True, comment="Storage plan name")
    pricing = Column(
        Numeric(precision=10, scale=2), default=0, comment="Price in currency"
    )
    storage = Column(Integer, nullable=False, comment="Storage")
    period = Column(
        Integer,
        nullable=False,
        comment="Billing period (1: monthly, 3: yearly, etc.)",
    )
    is_active = Column(Boolean, default=True, nullable=False)
