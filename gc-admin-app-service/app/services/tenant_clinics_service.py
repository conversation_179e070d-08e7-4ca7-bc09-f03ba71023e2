import ast

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.constants import ClinicRedis<PERSON>ey, TenantClinicStatus
from db.db_connection import TenantDatabase
from schemas.tenant_clinic_requests import CreateClinicInfoSchema, TenantPayloads
from sqlalchemy import select, text
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models.clinic_tenants import TenantClinic
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import ClinicInformation, DoctorProfile, DoctorUser


class TenantClinicService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_tenant",
    )
    async def create_tenant(self, data: TenantPayloads) -> TenantClinic:
        tenant = TenantClinic(
            tenant_name=data.tenant_name,
            tenant_slug=data.tenant_slug,
            business_number=data.business_number,
            db_name=data.db_name,
            db_uri=data.db_uri,
            plan_id=data.plan_id,
        )
        async with self.session.begin():
            self.session.add(tenant)
            await self.session.flush()
            await self.session.refresh(tenant)
            return tenant

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_tenant_status",
    )
    async def update_tenant_status(self, tenant_uuid: str, status: int) -> bool:
        async with self.session.begin():
            result = await self.session.execute(
                text(
                    """
                    UPDATE tenant_clinics
                    SET status = :status
                    WHERE tenant_uuid = :tenant_uuid
                    RETURNING tenant_uuid
                """
                ),
                {"tenant_uuid": tenant_uuid, "status": status},
            )
            row = result.mappings().first()
            return row

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_db_name_for_tenant",
    )
    async def get_db_name_for_tenant(self, tenant_uuid: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                text(
                    "SELECT db_name FROM tenant_clinics WHERE tenant_uuid = :tenant_uuid"
                ),
                {"tenant_uuid": tenant_uuid},
            )
            db_name = result.scalar_one_or_none()
            return db_name

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_db_by_name",
    )
    async def get_tenant_db_by_name(self, tenant_name: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic.db_name).where(
                    TenantClinic.tenant_name == tenant_name
                )
            )
            db_name = result.scalar_one_or_none()
            return db_name

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_data_by_name",
    )
    async def get_tenant_data_by_name(self, tenant_name: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic).where(TenantClinic.tenant_name == tenant_name)
            )
            tenant_obj = result.scalar_one_or_none()
            return tenant_obj

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_info_by_business_number",
    )
    async def get_tenant_info_by_business_number(
        self, business_number: str
    ) -> TenantClinic:
        async with self.session:
            stmt = select(TenantClinic).where(
                TenantClinic.business_number == business_number
            )
            result = await self.session.execute(stmt)
            tenant = result.scalar_one_or_none()
            return tenant

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="init_admin_clinic",
    )
    async def create_tenant_clinic_info(
        self, data: CreateClinicInfoSchema
    ) -> TenantClinic:
        """
        Create a new clinic account record in the database.
        :param data: Data for creating a clinic.
        :return: The id created clinic object.
        """
        token = set_current_db_name(data.clinic_db_name)
        try:
            session = await TenantDatabase.get_instance_tenant_db()
            clinic_ifo = ClinicInformation(
                **data.model_dump(exclude={"manager_info", "clinic_db_name"})
            )
            admin_user = DoctorUser(
                username=data.manager_info.email,
                required_change_password=data.manager_info.required_change_password,
            )
            # TODO : create Role Admin for doctorUser
            admin_user.set_password(data.manager_info.password, data.clinic_uuid)

            async with session.begin():
                # Add clinic information
                session.add(clinic_ifo)
                await session.flush()
                await session.refresh(clinic_ifo)

                # Add admin user
                session.add(admin_user)
                await session.flush()
                await session.refresh(admin_user)

                admin_profile = DoctorProfile(
                    doctor_user_id=admin_user.id,
                    **data.manager_info.model_dump(
                        exclude={
                            "password",
                            "required_change_password",
                            "is_active",
                        },
                        mode="json",
                    ),
                )

                # Encrypt sensitive fields
                aes = AesGCMRotation(configuration)
                admin_profile.phone_hash = aes.sha256_hash(admin_profile.phone)
                admin_profile.email_hash = aes.sha256_hash(admin_profile.email)
                admin_profile.date_of_birth_hash = aes.sha256_hash(
                    admin_profile.date_of_birth
                )
                admin_profile.phone = aes.encrypt_data(admin_profile.phone)
                admin_profile.email = aes.encrypt_data(admin_profile.email)
                admin_profile.date_of_birth = aes.encrypt_data(
                    admin_profile.date_of_birth
                )

                session.add(admin_profile)
                await session.flush()
                await session.refresh(admin_profile)
            return admin_user.id
        except (OperationalError, DBAPIError) as e:
            log.error(f"❌ Database error creating clinic info: {str(e)}")
            raise e
        except Exception as e:
            log.error(f"❌ Error creating clinic info: {str(e)}")
            return None
        finally:
            reset_current_db_name(token)
            await self.delete_cache_clinic_slug()

    async def delete_cache_clinic_slug(self) -> None:
        """
        Delete the clinic slug cache.
        :return: None
        """
        try:
            redis_cli = await RedisCli.get_instance(configuration)
            prefix = ClinicRedisKey.CLINICS_CACHE.value
            await redis_cli.delete(prefix)
        except Exception as e:
            log.error(f"❌ Error setting cache for clinic slugs: {str(e)}")

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_list_clinic_slugs",
    )
    async def get_list_clinic_from_central_db(self):
        """
        Get a list of all clinics.
        :return: List of ClinicInformation objects.
        """
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic.tenant_slug).where(
                    TenantClinic.status == TenantClinicStatus.SUCCESS.value
                )
            )
            rows = result.scalars().all()
            return [row for row in rows if row is not None]

    async def set_cache_clinic_slug(self, list_clinic: list[str]) -> None:
        """
        Set the clinic slug in cache.
        :param tenant_slug: The slug of the clinic.
        """
        try:
            redis_cli = await RedisCli.get_instance(configuration)
            prefix = ClinicRedisKey.CLINICS_CACHE.value

            if list_clinic:
                await redis_cli.set(prefix, str(list_clinic))
            else:
                await redis_cli.delete(prefix)
        except Exception as e:
            log.error(f"❌ Error setting cache for clinic slugs: {str(e)}")

    async def get_cache_clinic_slug(self) -> list[str]:
        """
        Get the clinic slug from cache.
        :return: List of clinic slugs.
        """
        redis_cli = await RedisCli.get_instance(configuration)
        prefix = ClinicRedisKey.CLINICS_CACHE.value
        cached_value = await redis_cli.get(prefix)

        if cached_value:
            try:
                list_clinic = ast.literal_eval(cached_value)
                if isinstance(list_clinic, list):
                    return list_clinic
            except (SyntaxError, ValueError) as e:
                log.error(f"❌ Error parsing cached clinic slugs: {str(e)}")
        return []

    async def check_clinic_slug_exists(self, tenant_slug: str) -> bool:
        """
        Check if the clinic slug exists in the cache.
        :param tenant_slug: The slug of the clinic.
        :return: True if the slug exists, False otherwise.
        """
        list_clinic = await self.get_cache_clinic_slug()
        if not list_clinic:
            list_clinic = await self.get_list_clinic_from_central_db()
            await self.set_cache_clinic_slug(list_clinic)

        return tenant_slug in list_clinic if list_clinic else False
