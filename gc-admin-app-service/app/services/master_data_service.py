import json
from typing import Optional

from core.messages import CustomMessageCode
from enums.master_data_enum import MappingModel, MasterModelEnum
from schemas.requests.master_data_schema import (
    MasterItemsrequestData,
    MasterRequestData,
)
from schemas.responses.master_data_schema import (
    MasterResponseData,
    MasterResponseDataItems,
    MasterResponseSchema,
)
from sqlalchemy import select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.logger.config import log


class MasterDataService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_master_data(
        self, master_names: MasterRequestData
    ) -> MasterResponseSchema:
        """
        Fetch master data based on the provided master names.
        :param master_names: Data containing the list of master model names.
        :return: A schema containing the master data.
        """
        results = []

        for master_name in master_names.master_names:
            result = await self.get_master_data_by_model_name(master_name)
            if result:
                results.append(result.model_dump())
            else:
                results.append(
                    MasterResponseData(
                        master_name=master_name,
                        master_datas=[],
                        msg=f"No data found for {master_name}",
                    )
                )

        return MasterResponseSchema(data=results)

    def to_dict(self, obj):
        """
        Convert SQLAlchemy model instance to dictionary.
        :param obj: SQLAlchemy model instance.
        :return: Dictionary representation of the model instance.
        """
        data = obj.__dict__.copy()
        data.pop("_sa_instance_state", None)
        return data

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_master_data",
    )
    async def get_master_data_by_model_name(
        self, master_name: MasterModelEnum
    ) -> MasterResponseData:
        async with self.session:
            model = MappingModel().get_model(master_name)
            pk_column = MappingModel().get_pk_column_model(master_name)

            try:
                stmt = select(model)
                if pk_column is not None:
                    stmt = stmt.order_by(getattr(model, pk_column.key))

                data = await self.session.execute(stmt)
                data = data.scalars().all()

                if data:
                    return MasterResponseData(
                        master_name=master_name,
                        master_datas=[self.to_dict(item) for item in data],
                        msg=CustomMessageCode.MASTER_DATA_FETCH_SUCCESS.title,
                    )
                msg = CustomMessageCode.MASTER_DATA_NOT_FOUND.title
            except Exception as e:
                log.error(f"Error fetching data for {master_name.value}: {str(e)}")
                msg = CustomMessageCode.MASTER_DATA_FETCH_FAILED.title

            return MasterResponseData(master_name=master_name, master_datas=[], msg=msg)

    def validate_master_data(
        self,
        master_name: MasterModelEnum,
        items: list[str],
        filter: Optional[str] = None,
    ) -> MasterItemsrequestData:
        """
        Validate if the specified column exists in the master model.
        :param master_name: The name of the master model.
        :param column_name: The list of column names to validate.
        :return: details of the validation result.
        """
        master_model = MappingModel().get_model(master_name)
        if not master_model or (not items and not filter):
            raise ValueError(CustomMessageCode.MASTER_DATA_NOT_FOUND.title)

        fields = MappingModel().get_model_fields(master_name)
        if not all(field in fields for field in items):
            log.error(
                f"❌ Column(s) {items} do not exist in the master model {master_name}."
            )
            raise ValueError(CustomMessageCode.MASTER_DATA_COLUMN_NOT_EXIST.title)

        try:
            filter_condition = json.loads(filter) if filter else {}
        except Exception as e:
            log.error(f"❌ Invalid filter condition: {str(e)}")
            raise ValueError(CustomMessageCode.MASTER_DATA_INVALID_FILTER.title)

        for key in filter_condition.keys():
            if key not in fields:
                log.error(
                    f"❌ Filter key '{key}' does not exist in the master model {master_name.value}."
                )
                raise ValueError(CustomMessageCode.MASTER_DATA_COLUMN_NOT_EXIST.title)

        return MasterItemsrequestData(
            master_model=master_model, items=items, filter=filter_condition
        )

    def build_data_query(self, obj: MasterItemsrequestData) -> select:
        """
        Build a query to fetch data from the master model based on items and filter.
        :param master_name: The name of the master model.
        :param items: The list of items to filter.
        :param filter: The filter condition for the items.
        :return: A SQLAlchemy query object.
        """
        model = obj.master_model
        stmt = select(model.__table__.columns)
        if obj.items:
            stmt = select(*(getattr(model, item) for item in obj.items))

        if obj.filter:
            stmt = stmt.filter(*(getattr(model, k) == v for k, v in obj.filter.items()))

        return stmt

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="validate_column_existence",
    )
    async def get_master_data_items(self, query: select) -> MasterResponseDataItems:
        """
        Execute the query to fetch data based on the provided query object.
        :param query: The SQLAlchemy query object.
        :return: A list of dictionaries representing the fetched data.
        """
        async with self.session:
            try:
                result = await self.session.execute(query)
                data = result.mappings().all()
                return MasterResponseDataItems(data=data)
            except Exception as e:
                log.error(f"❌ Error executing query: {str(e)}")
                raise ValueError(CustomMessageCode.MASTER_DATA_FETCH_FAILED.title)
