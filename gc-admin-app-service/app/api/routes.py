from api.health_check import router as health_check_router
from api.v1.import_file_api import router as import_file_router
from api.v1.master.master_api import router as master_router
from api.v1.medical_device_api import router as medical_device_router
from api.v1.pricing.pricing_storages_api import router as pricing_storage_router
from api.v1.tenant.tenants_api import router as tenant_router
from api.v1.thumbnail_api import router as thumbnail_router
from core.api_version_router import VersionedAPIRouter

router = VersionedAPIRouter()

router.include_router(master_router, prefix="/master-data", tags=["Master data"])
router.include_router(tenant_router, prefix="/tenants", tags=["Tenants"])
router.include_router(
    import_file_router,
    prefix="/import-files",
    tags=["Import file from external sources"],
)
router.include_router(
    medical_device_router, prefix="/medical-devices", tags=["Medical device"]
)
router.include_router(
    thumbnail_router,
    prefix="/thumbnails",
    tags=["Thumbnail"],
)
router.include_router(
    pricing_storage_router,
    prefix="/pricing-storages",
    tags=["Pricing storages"],
)
router.include_router(health_check_router)
