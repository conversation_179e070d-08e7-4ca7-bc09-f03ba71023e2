from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends, Query
from schemas.requests.master_data_schema import MasterRequestData
from services.master_data_service import MasterDataService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.get("")
@version(1, 0)
async def get_master_data(
    obj: Annotated[
        MasterRequestData, Query(..., description="List of master model name")
    ],
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    try:
        service = MasterDataService(session=db_session)
        response = await service.get_master_data(master_names=obj)
        return ApiResponse.success(
            data=response.model_dump(mode="json").get("data", [])
        )
    except Exception as e:
        log.error(f"❌ Error fetching master data: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.MASTER_DATA_NOT_FOUND.title,
            message_code=CustomMessageCode.MASTER_DATA_NOT_FOUND.code,
        )
