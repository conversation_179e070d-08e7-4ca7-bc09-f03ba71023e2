from typing import Annotated, Optional

from configuration.settings import configuration
from core.common.api_response import Api<PERSON><PERSON>ponse
from gc_dentist_shared.core.common.redis import RedisCli
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, status
from fastapi.params import Depends
from fastapi_pagination import Page, Params
from schemas.requests.pricing import ExtraStorageRequestSchema
from schemas.responses.pricing import TenantExtraStorageResponse
from services.pricing_service import PricingService
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.responses import JSONResponse

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("/extra")
@version(1, 0)
@measure_time
async def create_pricing_extra_storage(
    request_data: ExtraStorageRequestSchema,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    try:
        service = PricingService(db_session)
        result = await service.create_extra_storage(request_data, configuration)
        return ApiResponse.success(
            data=result.id,
            message=CustomMessageCode.PRICING_EXTRA_STORAGE_CREATED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create_pricing_extra_storage CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Create Pricing Extra Storage Error: {}".format(str(e)))
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "detail": CustomMessageCode.PRICING_EXTRA_STORAGE_CREATED_FAILED.title
            },
        )


@router.get("/{tenant_uuid}/extra", response_model=Page[TenantExtraStorageResponse])
@version(1, 0)
@measure_time
async def get_list_pricing_extra_storage(
    params: Annotated[Params, Depends()],
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
    tenant_uuid: str,
    search: Optional[str] = None,
):
    try:
        service = PricingService(db_session)
        result = await service.get_list_pricing_extra_storage(
            tenant_uuid, params, search
        )
        return ApiResponse.success(data=result.model_dump(mode="json"))
    except CustomValueError as e:
        log.error(
            f"❌ Error get_list_pricing_extra_storage CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Get List Pricing Extra Storage Error: {}".format(str(e)))
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "detail": CustomMessageCode.PRICING_EXTRA_STORAGE_GET_LIST_FAILED.title
            },
        )


@router.put("/{tenant_uuid}/plans/{plan_key_id:int}")
@version(1, 0)
@measure_time
async def update_plan_storage(
    plan_key_id: int,
    tenant_uuid: str,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
    redis_cli: Annotated[RedisCli, Depends(RedisCli.get_instance)],
):
    try:
        service = PricingService(db_session)
        result = await service.update_plan_storage(plan_key_id, tenant_uuid, redis_cli)
        return ApiResponse.success(
            data=result.id,
            message=CustomMessageCode.PRICING_PLAN_STORAGE_UPDATED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error update_plan_storage CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Update Plan Storage Error: {}".format(str(e)))
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "detail": CustomMessageCode.PRICING_PLAN_STORAGE_UPDATED_FAILED.title
            },
        )
