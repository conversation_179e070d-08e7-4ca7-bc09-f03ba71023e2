import os
from logging.config import fileConfig

from alembic import context
from sqlalchemy import engine_from_config, pool

from gc_dentist_shared.base.tenant_declarative_base import TenantBase
from gc_dentist_shared.tenant_models import *  # noqa: F401, F403

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = TenantBase.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.
x_args = context.get_x_argument(as_dictionary=True)

if x_args.get("dev", "false") == "true":
    config.set_main_option(
        "sqlalchemy.url",
        "postgresql://postgres:Tuantran123456^@localhost:5432/tenant_template",
        # CentralDatabase.get_url_db_sync(
        #     Settings().POSTGERS_TENANT_TEMPLATE_DB_NAME
        # ).render_as_string(hide_password=False),
    )


# def get_db_uri():
#     return config.get_main_option("sqlalchemy.url")


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


# async def run_migrations_online():
#     connectable = create_async_engine(
#         get_db_uri(),
#         poolclass=pool.NullPool,
#     )

#     async with connectable.connect() as connection:
#         await connection.run_sync(do_migrations)

# def do_migrations(connection):
#     context.configure(
#         connection=connection,
#         target_metadata=target_metadata,
#         compare_type=True,
#         compare_server_default=True,
#         transactional_ddl=True
#     )
#     with context.begin_transaction():
#         context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        os.environ["ALEMBIC_CONTEXT"] = "1"
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            transactional_ddl=True,
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
