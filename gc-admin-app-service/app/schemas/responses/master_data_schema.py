from typing import ClassVar

from pydantic import BaseModel, Field


class MasterResponseData(BaseModel):
    master_name: str = Field(..., description="Name of the master model")
    master_datas: list[dict] = Field(
        default=[], description="List of data associated with the master model"
    )
    msg: str = Field(
        default="Success!", description="Message associated with the master model data"
    )

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "master_name": "ExampleMasterModel",
                "master_datas": [
                    {"key1": "value1", "key2": "value2"},
                    {"key1": "value3", "key2": "value4"},
                ],
                "msg": "Success!",
            }
        }


class MasterResponseSchema(BaseModel):
    data: list[MasterResponseData] = Field(
        ..., description="List of master response data"
    )

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "data": [
                    {
                        "master_name": "ExampleMasterModel",
                        "master_datas": [
                            {"key1": "value1", "key2": "value2"},
                            {"key1": "value3", "key2": "value4"},
                        ],
                        "msg": "Success!",
                    }
                ]
            }
        }


class MasterResponseDataItems(BaseModel):
    data: list[dict] = Field(
        default=[], description="List of items associated with the master model"
    )
