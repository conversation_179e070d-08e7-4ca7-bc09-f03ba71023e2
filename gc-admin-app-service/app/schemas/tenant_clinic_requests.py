from datetime import datetime
from typing import Any, Optional

from core.constants import GenderEnum
from core.messages import CustomMessageCode
from pydantic import BaseModel, Field, conint, constr, field_validator, root_validator

from gc_dentist_shared.core.common.utils import (
    CustomEmailStr,
    PhoneNumberExistCountryCode,
    ValidateDateString,
    convert_datetime_with_timezone,
)
from gc_dentist_shared.core.common.validator import password_validator


class ClinicInfoSchema(BaseModel):
    clinic_name: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Name of the clinic",
    )
    phone_number: PhoneNumberExistCountryCode = Field(
        ..., strip_whitespace=True, description="Phone number of the clinic"
    )
    email: CustomEmailStr = Field(
        ..., strip_whitespace=True, description="Email address of the clinic"
    )
    address_1: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Address_1 of the clinic",
    )
    address_2: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Address_2 of the clinic",
    )
    address_3: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Address_3 of the clinic",
    )
    latitude: Optional[str] = Field(
        None,
        strip_whitespace=True,
        description="Latitude of the clinic location",
    )
    longitude: Optional[str] = Field(
        None,
        strip_whitespace=True,
        description="Longitude of the clinic location",
    )
    logo_url: Optional[str] = Field(
        None, strip_whitespace=True, description="Logo URL of the clinic"
    )
    opening_hours: Optional[dict[str, Any]] = Field(
        None, description="Opening hours of the clinic in JSON format"
    )


class ManagerTenant(BaseModel):
    phone: str = Field(
        ...,
        min_length=10,
        max_length=11,
        strip_whitespace=True,
        description="Phone number of the clinic admin",
    )
    country_code: str = Field(
        ...,
        min_length=2,
        max_length=3,
        strip_whitespace=True,
        description="Country code of the clinic admin's phone number",
    )
    gender: GenderEnum = Field(..., description="Gender for clinic admin")
    date_of_birth: ValidateDateString = Field(
        ...,
        description="Date of birth for clinic admin",
    )
    first_name: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="First name of the clinic admin",
    )
    last_name: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Last name of the clinic admin",
    )
    email: CustomEmailStr = Field(
        ...,
        strip_whitespace=True,
        description="Username for the clinic admin",
    )
    password: str = Field(
        ...,
        min_length=8,
        strip_whitespace=True,
        description="Password for the clinic admin",
    )
    required_change_password: bool = Field(
        True,
        description="Is the clinic admin required to change password on first login?",
    )
    is_active: bool = Field(True, description="Is the clinic currently active?")

    @field_validator("password")
    @classmethod
    def validate_password(cls, value: str) -> str:
        return password_validator(value)

    @field_validator("date_of_birth")
    @classmethod
    def validate_date_of_birth(cls, value):
        now = convert_datetime_with_timezone(datetime.now().astimezone()).date()
        if value > now:
            raise ValueError(CustomMessageCode.VALUE_ERROR_INVALID_DATE_PAST.title)
        return value


class TenantPayloads(BaseModel):
    tenant_name: constr(strip_whitespace=True, min_length=1)  # type: ignore
    tenant_slug: constr(strip_whitespace=True, min_length=1)  # type: ignore
    business_number: str = Field(..., min_length=1, strip_whitespace=True)
    db_name: constr(strip_whitespace=True, min_length=1)  # type: ignore
    db_uri: Optional[constr(strip_whitespace=True, min_length=1)] = None  # type: ignore
    clinic_admin_username: Optional[constr(strip_whitespace=True, min_length=1)] = None  # type: ignore
    clinic_admin_password: Optional[constr(strip_whitespace=True, min_length=1)] = None  # type: ignore
    plan_id: conint(gt=0)  # type: ignore
    # config: Optional[dict] = {}

    @root_validator(pre=True)
    @classmethod
    def build_db_name(cls, values):
        tenant_name = values.get("tenant_name")
        db_name = values.get("db_name")
        if not db_name:
            if tenant_name:
                values["db_name"] = tenant_name.strip().replace(" ", "_").lower()
        return values

    @field_validator("business_number")
    @classmethod
    def validate_business_number(cls, value: str) -> str:
        value = value.strip()
        if not value:
            raise ValueError("Business number cannot be empty or whitespace.")
        # TODO: Add additional validation for business number format
        return value


class CreateTenantSchema(TenantPayloads):
    clinic_info: ClinicInfoSchema
    manager_info: ManagerTenant


class CreateClinicInfoSchema(ClinicInfoSchema):
    clinic_uuid: str = Field(
        ..., strip_whitespace=True, description="UUID of the clinic"
    )
    clinic_slug: str = Field(
        ..., strip_whitespace=True, description="Slug of the clinic"
    )
    clinic_db_name: str = Field(
        ..., strip_whitespace=True, description="Database name of the clinic"
    )
    manager_info: ManagerTenant


class TenantApplyChanges(BaseModel):
    tenant_uuid: constr(strip_whitespace=True, min_length=1)  # type: ignore
    db_name: constr(strip_whitespace=True, min_length=1)  # type: ignore
    db_uri: Optional[constr(strip_whitespace=True, min_length=1)] = None  # type: ignore


class MigrationTenantPayloads(BaseModel):
    databases: list[TenantApplyChanges]

    # metadata: Dict[str, Any] = Field(default_factory=dict)

    # @field_validator("payloads", mode="after")
    # def exclude_existing_fields(
    #     cls, v: Dict[str, Any], values: Dict[str, Any]
    # ) -> Dict[str, Any]:
    #     """
    #     Delete existing fields in the model
    #     """
    #     if not isinstance(v, dict):
    #         return v

    #     existing_fields = set(values.data.keys())

    #     filtered_payloads = v.copy()

    #     for field in existing_fields:
    #         filtered_payloads.pop(field, None)

    #     return filtered_payloads
