from enum import Enum
from typing import NamedTuple, Optional


class PeriodInfo(NamedTuple):
    period_id: int
    days: int


class PricingStoragePeriod(Enum):
    WEEKLY = PeriodInfo(period_id=1, days=7)
    MONTHLY = PeriodInfo(period_id=3, days=30)
    QUARTERLY = PeriodInfo(period_id=5, days=90)
    YEARLY = PeriodInfo(period_id=7, days=365)
    NEVER_EXPIRED = PeriodInfo(period_id=9, days=0)

    @property
    def period_id(self) -> int:
        return self.value.period_id

    @property
    def days(self) -> int:
        return self.value.days

    @classmethod
    def get_days_by_period_id(cls, period_id: int) -> Optional[int]:
        for period in cls:
            if period.period_id == period_id:
                return period.days
        return None

    @classmethod
    def get_by_period_id(cls, period_id: int) -> Optional["PricingStoragePeriod"]:
        for period in cls:
            if period.period_id == period_id:
                return period
        return None
