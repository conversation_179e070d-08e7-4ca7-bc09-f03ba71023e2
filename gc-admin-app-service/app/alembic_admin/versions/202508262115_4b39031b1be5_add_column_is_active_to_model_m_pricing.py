"""add column is active to model m_pricing

Revision ID: 4b39031b1be5
Revises: e1e9e418d2d9
Create Date: 2025-08-26 21:15:00.586570

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4b39031b1be5'
down_revision: Union[str, None] = 'e1e9e418d2d9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('m_pricing_storages', sa.Column('is_active', sa.<PERSON>(), nullable=False, server_default=sa.text("TRUE")))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('m_pricing_storages', 'is_active')
    # ### end Alembic commands ###
