[project]
name = "gc-admin-app-service"
version = "0.1.0"
description = ""
authors = [
    {name = "Name",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "uvicorn (>=0.35.0,<0.36.0)",
    "fastapi (>=0.116.1,<0.117.0)",
    "pydantic-settings (>=2.10.1,<3.0.0)",
    "sqlalchemy (>=2.0.43,<3.0.0)",
    "pydantic-extra-types (>=2.10.5,<3.0.0)",
    "phonenumbers (>=9.0.12,<10.0.0)",
    "authlib (>=1.6.1,<2.0.0)",
    "python-multipart (>=0.0.20,<0.0.21)",
    "pydantic[email] (>=2.11.7,<3.0.0)",
    "alembic (>=1.16.4,<2.0.0)",
    "aiobotocore (>=2.24,<3.0)",
    "boto3 (>=1.39,<2.0)",
    "redis (>=6.4.0,<7.0.0)",
    "sqlalchemy-utils (>=0.41.2,<0.42.0)",
    "firebase-admin (>=7.1.0,<8.0.0)",
    "fastapi-pagination (>=0.13.3,<0.14.0) ; python_version < \"4.0\"",
    "asyncpg (>=0.30.0,<0.31.0)",
    "greenlet (>=3.2.4,<4.0.0)",
    "psycopg2 (>=2.9.10,<3.0.0)",
    "babel (>=2.17.0,<3.0.0)",
    "pre-commit (>=4.3.0,<5.0.0)",
    "pytest (>=8.4.1,<9.0.0)",
    "httpx (>=0.28.1,<0.29.0)",
    "cryptography (>=45.0.6,<46.0.0)",
    "werkzeug (>=3.1.3,<4.0.0)",
    "fastapi-versioning (>=0.10.0,<0.11.0)",
    "msgspec (>=0.19.0,<0.20.0)",
    "starlette-context (>=0.4.0,<0.5.0) ; python_version < \"4.0\"",
    "sqlparse (>=0.5.3,<0.6.0)",
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"


[tool.poetry]
package-mode = false
